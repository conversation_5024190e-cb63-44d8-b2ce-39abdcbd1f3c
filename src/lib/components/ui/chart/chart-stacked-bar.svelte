<script lang="ts">
	import * as Card from '$lib/components/ui/card/index.js';
	import * as Chart from '$lib/components/ui/chart/index.js';
	import { scaleBand } from 'd3-scale';
	import { Bar<PERSON><PERSON>, Highlight, type ChartContextValue } from 'layerchart';
	import TrendingUpIcon from '@lucide/svelte/icons/trending-up';
	import { cubicInOut } from 'svelte/easing';

	interface StackedBarData {
		[key: string]: string | number;
	}

	interface StackedBarProps {
		data: StackedBarData[];
		chartConfig: Chart.ChartConfig;
		xKey: string;
		title?: string;
		description?: string;
		footerText?: string;
		footerSubtext?: string;
		showTrending?: boolean;
		height?: number;
	}

	let {
		data,
		chartConfig,
		xKey,
		title = 'Stacked Bar Chart',
		description = '',
		footerText = '',
		footerSubtext = '',
		showTrending = false,
		height = 400,
	}: StackedBarProps = $props();

	let context = $state<ChartContextValue>();

	// Extract series keys from chartConfig (excluding the x-axis key)
	const seriesKeys = $derived(Object.keys(chartConfig).filter((key) => key !== xKey));

	// Create series configuration for LayerChart
	const series = $derived(
		seriesKeys.map((key, index) => {
			return {
				key,
				label: chartConfig[key]?.label || key,
				color: chartConfig[key]?.color || '#000000', // Simple fallback color
				props: index === 0 ? { rounded: 'bottom' as const } : undefined,
			};
		}),
	);
</script>

<Card.Root>
	<Card.Header>
		<Card.Title>{title}</Card.Title>
		{#if description}
			<Card.Description>{description}</Card.Description>
		{/if}
	</Card.Header>
	<Card.Content>
		<Chart.Container config={chartConfig} class="aspect-auto w-full">
			<div style="height: {height}px; width: 100%;">
				<BarChart
					bind:context
					{data}
					xScale={scaleBand().padding(0.25)}
					x={xKey}
					axis="x"
					rule={false}
					{series}
					seriesLayout="stack"
					props={{
						bars: {
							stroke: 'none',
							initialY: context?.height,
							initialHeight: 0,
							motion: {
								y: { type: 'tween', duration: 500, easing: cubicInOut },
								height: { type: 'tween', duration: 500, easing: cubicInOut },
							},
						},
						highlight: { area: false },
						xAxis: {
							format: (d) => (typeof d === 'string' && d.length > 10 ? d.slice(0, 10) + '...' : d),
						},
					}}
					legend
				>
					{#snippet belowMarks()}
						<Highlight area={{ class: 'fill-muted' }} />
					{/snippet}
					{#snippet tooltip()}
						<Chart.Tooltip />
					{/snippet}
				</BarChart>
			</div>
		</Chart.Container>
	</Card.Content>
	{#if footerText || footerSubtext || showTrending}
		<Card.Footer>
			<div class="flex w-full items-start gap-2 text-sm">
				<div class="grid gap-2">
					{#if showTrending && footerText}
						<div class="flex items-center gap-2 leading-none font-medium">
							{footerText}
							<TrendingUpIcon class="size-4" />
						</div>
					{:else if footerText}
						<div class="flex items-center gap-2 leading-none font-medium">
							{footerText}
						</div>
					{/if}
					{#if footerSubtext}
						<div class="text-muted-foreground flex items-center gap-2 leading-none">
							{footerSubtext}
						</div>
					{/if}
				</div>
			</div>
		</Card.Footer>
	{/if}
</Card.Root>
