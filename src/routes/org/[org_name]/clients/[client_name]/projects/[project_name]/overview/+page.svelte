<script lang="ts">
	import type { PageProps } from './$types';
	import { createStackedBudgetData, createStackedBudgetChartConfig } from '$lib/budget_utils';
	import StackedBarChart from '$lib/components/ui/chart/chart-stacked-bar.svelte';
	import * as Select from '$lib/components/ui/select';
	import * as Card from '$lib/components/ui/card';
	import { formatCurrency } from '$lib/utils';

	let { data }: PageProps = $props();

	let depth = $state(3);

	// Depth options for the selector
	const depthOptions = [
		{ value: '1', label: 'Level 1 (Top Level)' },
		{ value: '2', label: 'Level 2' },
		{ value: '3', label: 'Level 3' },
		{ value: '4', label: 'Level 4' },
	];

	// Create stacked chart data including current budget as a "snapshot"
	const stackedChartData = $derived.by(() => {
		// Combine current budget with snapshots
		const allSnapshotsData = [
			// Add all actual snapshots
			...data.snapshotsWithItems,
			// Add current budget as the last "snapshot"
			{
				snapshot: {
					budget_snapshot_id: 'current',
					project_stage_id: 'current',
					freeze_date: new Date().toISOString(),
					freeze_reason: null,
					created_by_user_id: 'current',
					created_at: new Date().toISOString(),
					updated_at: new Date().toISOString(),
					project_stage: {
						name: 'Current Budget',
						stage_order: data.projectStages.length,
						stage: data.projectStages.length,
						project_id: data.project.project_id,
					},
				},
				budgetItems: data.rawCurrentItems,
			},
		];

		if (allSnapshotsData.length === 0) {
			return [];
		}

		return createStackedBudgetData(data.allWbsItems, allSnapshotsData, depth);
	});

	// Create chart configuration
	const chartConfig = $derived.by(() => {
		const chartData = stackedChartData;
		if (chartData.length === 0) {
			return {};
		}

		// Extract WBS codes from the first data item (excluding 'snapshot' key)
		const firstItem = chartData[0];
		const wbsCodes = Object.keys(firstItem).filter((key) => key !== 'snapshot');

		return createStackedBudgetChartConfig(wbsCodes, data.allWbsItems);
	});

	// Calculate total budget value across all snapshots for footer
	const totalBudgetValue = $derived(() => {
		const chartData = stackedChartData;
		if (chartData.length === 0) return 0;

		const latestSnapshot = chartData[chartData.length - 1];
		return Object.keys(latestSnapshot)
			.filter((key) => key !== 'snapshot')
			.reduce((sum, key) => sum + (latestSnapshot[key] as number), 0);
	});
</script>

<div class="container">
	<h1 class="sr-only text-3xl font-semibold">Overview</h1>

	<div class="mt-8 space-y-6">
		<!-- Budget Snapshots Stacked Bar Chart -->
		<Card.Root>
			<Card.Header>
				<div class="flex items-center justify-between">
					<div>
						<Card.Title>Budget Evolution</Card.Title>
						<Card.Description>
							Budget values across project stages at WBS depth level {depth}
						</Card.Description>
					</div>
					<div class="flex items-center gap-2">
						<label for="depth-select" class="text-sm font-medium">WBS Depth:</label>
						<Select.Root
							type="single"
							value={depth.toString()}
							onValueChange={(value) => {
								if (value) {
									depth = parseInt(value);
								}
							}}
						>
							<Select.Trigger class="w-40">
								{depthOptions.find((opt) => opt.value === depth.toString())?.label || 'Level 3'}
							</Select.Trigger>
							<Select.Content>
								{#each depthOptions as option (option.value)}
									<Select.Item value={option.value}>{option.label}</Select.Item>
								{/each}
							</Select.Content>
						</Select.Root>
					</div>
				</div>
			</Card.Header>
			<Card.Content>
				{#if stackedChartData.length > 0 && Object.keys(chartConfig).length > 0}
					<StackedBarChart
						data={stackedChartData}
						{chartConfig}
						xKey="snapshot"
						title=""
						description=""
						footerText={`Total Budget: ${formatCurrency(totalBudgetValue())}`}
						footerSubtext={`Showing ${stackedChartData.length} budget version${stackedChartData.length === 1 ? '' : 's'} at WBS depth ${depth}`}
						height={400}
					/>
				{:else}
					<div class="text-muted-foreground flex h-64 items-center justify-center">
						<p>No budget data available for the selected depth level.</p>
					</div>
				{/if}
			</Card.Content>
		</Card.Root>
	</div>
</div>

<pre>
	{JSON.stringify(stackedChartData, null, 2)}
</pre>
