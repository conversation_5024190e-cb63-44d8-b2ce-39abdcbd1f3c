import { describe, it, expect } from 'vitest';
import {
	createStackedBudgetData,
	createStackedBudgetChartConfig,
	type BudgetSnapshot,
} from '$lib/budget_utils';
import type { Tables } from '$lib/database.types';

// Helper function to create a WBS item
function createWbsItem(
	id: string,
	parentId: string | null,
	code: string,
	description: string,
	level: number,
): Tables<'wbs_library_item'> {
	return {
		wbs_library_item_id: id,
		wbs_library_id: '550e8400-e29b-41d4-a716-446655440001',
		level,
		in_level_code: code.split('.').pop() || code,
		parent_item_id: parentId,
		code,
		description,
		cost_scope: null,
		item_type: 'Standard',
		client_id: null,
		project_id: null,
		created_at: '2024-01-01T00:00:00Z',
		updated_at: '2024-01-01T00:00:00Z',
	};
}

// Helper function to create a budget snapshot
function createBudgetSnapshot(id: string, stageName: string, stageOrder: number): BudgetSnapshot {
	return {
		budget_snapshot_id: id,
		project_stage_id: `stage-${stageOrder}`,
		freeze_date: '2024-01-01T00:00:00Z',
		freeze_reason: null,
		created_by_user_id: 'user-1',
		created_at: '2024-01-01T00:00:00Z',
		updated_at: '2024-01-01T00:00:00Z',
		project_stage: {
			name: stageName,
			stage_order: stageOrder,
			stage: stageOrder,
			project_id: 'project-1',
		},
	};
}

// Helper function to create budget line items
function createBudgetLineItem(
	id: string,
	wbsItemId: string,
	quantity: number,
	unitRate: number,
	factor: number = 1,
) {
	return {
		budget_snapshot_line_item_id: id,
		budget_snapshot_id: 'snapshot-1',
		wbs_library_item_id: wbsItemId,
		quantity,
		unit: 'each',
		material_rate: unitRate,
		labor_rate: null,
		productivity_per_hour: null,
		unit_rate_manual_override: false,
		unit_rate: unitRate,
		factor,
		remarks: null,
		cost_certainty: null,
		design_certainty: null,
		created_at: '2024-01-01T00:00:00Z',
		label: 'snapshot-1',
	};
}

describe('Stacked Budget Chart Functions', () => {
	const wbsItems = [
		createWbsItem('1', null, '01', 'Site Work', 1),
		createWbsItem('2', '1', '01.01', 'Excavation', 2),
		createWbsItem('3', '1', '01.02', 'Utilities', 2),
		createWbsItem('4', null, '02', 'Structure', 1),
		createWbsItem('5', '4', '02.01', 'Foundation', 2),
		createWbsItem('6', '4', '02.02', 'Framing', 2),
	];

	describe('createStackedBudgetData', () => {
		it('should create stacked data for multiple snapshots at depth 1', () => {
			const snapshot1 = createBudgetSnapshot('snap-1', 'Design', 1);
			const snapshot2 = createBudgetSnapshot('snap-2', 'Construction', 2);

			const snapshotsData = [
				{
					snapshot: snapshot1,
					budgetItems: [
						createBudgetLineItem('item-1', '1', 10, 1000), // Site Work: 10,000
						createBudgetLineItem('item-2', '4', 5, 2000), // Structure: 10,000
					],
				},
				{
					snapshot: snapshot2,
					budgetItems: [
						createBudgetLineItem('item-3', '1', 12, 1100), // Site Work: 13,200
						createBudgetLineItem('item-4', '4', 6, 2200), // Structure: 13,200
					],
				},
			];

			const result = createStackedBudgetData(wbsItems, snapshotsData, 1);

			expect(result).toHaveLength(2);
			expect(result[0]).toEqual({
				snapshot: 'Design',
				'01': 10000,
				'02': 10000,
			});
			expect(result[1]).toEqual({
				snapshot: 'Construction',
				'01': 13200,
				'02': 13200,
			});
		});

		it('should create stacked data for depth 2', () => {
			const snapshot1 = createBudgetSnapshot('snap-1', 'Design', 1);

			const snapshotsData = [
				{
					snapshot: snapshot1,
					budgetItems: [
						createBudgetLineItem('item-1', '2', 5, 500), // 01.01: 2,500
						createBudgetLineItem('item-2', '3', 3, 800), // 01.02: 2,400
						createBudgetLineItem('item-3', '5', 2, 1500), // 02.01: 3,000
						createBudgetLineItem('item-4', '6', 4, 1200), // 02.02: 4,800
					],
				},
			];

			const result = createStackedBudgetData(wbsItems, snapshotsData, 2);

			expect(result).toHaveLength(1);
			expect(result[0]).toEqual({
				snapshot: 'Design',
				'01.01': 2500,
				'01.02': 2400,
				'02.01': 3000,
				'02.02': 4800,
			});
		});

		it('should filter out zero values', () => {
			const snapshot1 = createBudgetSnapshot('snap-1', 'Design', 1);

			const snapshotsData = [
				{
					snapshot: snapshot1,
					budgetItems: [
						createBudgetLineItem('item-1', '1', 10, 1000), // Site Work: 10,000
						createBudgetLineItem('item-2', '4', 0, 2000), // Structure: 0 (should be filtered)
					],
				},
			];

			const result = createStackedBudgetData(wbsItems, snapshotsData, 1);

			expect(result).toHaveLength(1);
			expect(result[0]).toEqual({
				snapshot: 'Design',
				'01': 10000,
			});
		});

		it('should use fallback mechanism when target depth data is missing', () => {
			// Create a scenario where one snapshot has detailed data at depth 2,
			// but another snapshot only has data at depth 1
			const snapshot1 = createBudgetSnapshot('snap-1', 'Early Budget', 1);
			const snapshot2 = createBudgetSnapshot('snap-2', 'Detailed Budget', 2);

			const snapshotsData = [
				{
					// Early snapshot: only has data at depth 1 (top level)
					snapshot: snapshot1,
					budgetItems: [
						createBudgetLineItem('item-1', '1', 10, 1000), // Site Work: 10,000 (depth 1)
						createBudgetLineItem('item-2', '4', 5, 2000), // Structure: 10,000 (depth 1)
					],
				},
				{
					// Later snapshot: has detailed data at depth 2
					snapshot: snapshot2,
					budgetItems: [
						createBudgetLineItem('item-3', '2', 3, 800), // Site Prep: 2,400 (depth 2)
						createBudgetLineItem('item-4', '3', 4, 600), // Excavation: 2,400 (depth 2)
						createBudgetLineItem('item-5', '5', 2, 1500), // Foundation: 3,000 (depth 2)
						createBudgetLineItem('item-6', '6', 3, 1200), // Framing: 3,600 (depth 2)
					],
				},
			];

			// Request data at depth 2
			const result = createStackedBudgetData(wbsItems, snapshotsData, 2);

			expect(result).toHaveLength(2);

			// First snapshot should use fallback data from depth 1
			// Since it doesn't have depth 2 data, it should include the depth 1 values
			expect(result[0]).toEqual({
				snapshot: 'Early Budget',
				'01': 10000, // Fallback from depth 1 Site Work
				'02': 10000, // Fallback from depth 1 Structure
				'01.01': 0, // Not present in early snapshot
				'01.02': 0, // Not present in early snapshot
				'02.01': 0, // Not present in early snapshot
				'02.02': 0, // Not present in early snapshot
			});

			// Second snapshot should use its detailed depth 2 data
			expect(result[1]).toEqual({
				snapshot: 'Detailed Budget',
				'01': 0, // Not present as fallback since depth 2 data exists
				'02': 0, // Not present as fallback since depth 2 data exists
				'01.01': 2400, // Site Prep
				'01.02': 2400, // Excavation
				'02.01': 3000, // Foundation
				'02.02': 3600, // Framing
			});
		});

		it('should handle complex multi-level fallback scenario like real project', () => {
			// Create a more complex WBS structure that matches real projects
			const complexWbsItems = [
				// Level 1
				createWbsItem('1', null, '01', 'Buildings', 1),
				// Level 2
				createWbsItem('2', '1', '01.02', 'Structure', 2),
				createWbsItem('3', '1', '01.05', 'Services and equipment', 2),
				// Level 3
				createWbsItem('4', '2', '01.02.03', 'Architectural works', 3),
				createWbsItem('5', '2', '01.02.04', 'Structural works', 3),
				createWbsItem('6', '3', '01.05.01', 'HVAC', 3),
				// Level 4
				createWbsItem('7', '4', '************', 'Doors and windows', 4),
				createWbsItem('8', '4', '************', 'Interior finishes', 4),
				createWbsItem('9', '5', '************', 'Concrete work', 4),
				createWbsItem('10', '5', '************', 'Steel work', 4),
			];

			// Scenario: 6 snapshots with different levels of detail
			const snapshots = [
				// Strategic: only level 2 data
				{
					snapshot: createBudgetSnapshot('snap-1', 'Strategic', 1),
					budgetItems: [
						createBudgetLineItem('item-1', '2', 100, 5000), // Structure: 500,000 (level 2)
						createBudgetLineItem('item-2', '3', 50, 3000), // Services: 150,000 (level 2)
					],
				},
				// Preparation: level 3 data (no level 4)
				{
					snapshot: createBudgetSnapshot('snap-2', 'Preparation', 2),
					budgetItems: [
						createBudgetLineItem('item-3', '4', 40, 4000), // Architectural: 160,000 (level 3)
						createBudgetLineItem('item-4', '5', 60, 3500), // Structural: 210,000 (level 3)
						createBudgetLineItem('item-5', '6', 30, 2000), // HVAC: 60,000 (level 3)
					],
				},
				// Detailed budgets: level 4 data
				{
					snapshot: createBudgetSnapshot('snap-3', 'Concept Design', 3),
					budgetItems: [
						createBudgetLineItem('item-6', '7', 20, 2000), // Doors: 40,000 (level 4)
						createBudgetLineItem('item-7', '8', 25, 1800), // Finishes: 45,000 (level 4)
						createBudgetLineItem('item-8', '9', 35, 2200), // Concrete: 77,000 (level 4)
						createBudgetLineItem('item-9', '10', 15, 3000), // Steel: 45,000 (level 4)
					],
				},
				{
					snapshot: createBudgetSnapshot('snap-4', 'Spatial Coordination', 4),
					budgetItems: [
						createBudgetLineItem('item-10', '7', 22, 2100), // Doors: 46,200 (level 4)
						createBudgetLineItem('item-11', '8', 28, 1900), // Finishes: 53,200 (level 4)
						createBudgetLineItem('item-12', '9', 38, 2300), // Concrete: 87,400 (level 4)
						createBudgetLineItem('item-13', '10', 18, 3100), // Steel: 55,800 (level 4)
					],
				},
			];

			// Test level 4 visualization
			const result = createStackedBudgetData(complexWbsItems, snapshots, 4);

			expect(result).toHaveLength(4);

			// Strategic snapshot should fall back to level 2
			expect(result[0]).toEqual({
				snapshot: 'Strategic',
				'01.02': 500000, // Fallback from level 2
				'01.05': 150000, // Fallback from level 2
				'01.02.03': 0, // Not present in strategic snapshot (from preparation snapshot)
				'01.02.04': 0, // Not present in strategic snapshot (from preparation snapshot)
				'01.05.01': 0, // Not present in strategic snapshot (from preparation snapshot)
				'************': 0,
				'************': 0,
				'************': 0,
				'************': 0,
			});

			// Preparation snapshot should fall back to level 3
			expect(result[1]).toEqual({
				snapshot: 'Preparation',
				'01.02': 0, // Not used as fallback since level 3 data exists
				'01.05': 0, // Not used as fallback since level 3 data exists
				'01.02.03': 160000, // Fallback from level 3 (architectural)
				'01.02.04': 210000, // Fallback from level 3 (structural)
				'01.05.01': 60000, // Fallback from level 3 (HVAC)
				'************': 0,
				'************': 0,
				'************': 0,
				'************': 0,
			});

			// Detailed snapshots should use level 4 data
			expect(result[2]).toEqual({
				snapshot: 'Concept Design',
				'01.02': 0,
				'01.05': 0,
				'01.02.03': 0,
				'01.02.04': 0,
				'01.05.01': 0,
				'************': 40000,
				'************': 45000,
				'************': 77000,
				'************': 45000,
			});
		});

		it('should handle real-world scenario: level 3 visualization with mixed data depths', () => {
			// Simulate the exact scenario described by the user:
			// - One snapshot with only level 2 data
			// - One snapshot with level 3 data (but not level 4)
			// - Four snapshots with level 4 data
			// When requesting level 3 visualization

			const realWorldWbsItems = [
				// Level 1
				createWbsItem('1', null, '01', 'Buildings', 1),
				// Level 2
				createWbsItem('2', '1', '01.02', 'Structure', 2),
				createWbsItem('3', '1', '01.05', 'Services', 2),
				// Level 3
				createWbsItem('4', '2', '01.02.03', 'Architectural works', 3),
				createWbsItem('5', '2', '01.02.04', 'Structural works', 3),
				createWbsItem('6', '3', '01.05.01', 'HVAC', 3),
				createWbsItem('7', '3', '01.05.02', 'Electrical', 3),
				// Level 4
				createWbsItem('8', '4', '************', 'Doors and windows', 4),
				createWbsItem('9', '4', '************', 'Interior finishes', 4),
				createWbsItem('10', '5', '************', 'Concrete work', 4),
				createWbsItem('11', '5', '************', 'Steel work', 4),
			];

			const realWorldSnapshots = [
				// Strategic: only level 2 data (should fall back to level 2 when requesting level 3)
				{
					snapshot: createBudgetSnapshot('snap-1', 'Strategic', 1),
					budgetItems: [
						createBudgetLineItem('item-1', '2', 100, 5000), // Structure: 500,000 (level 2)
						createBudgetLineItem('item-2', '3', 50, 3000), // Services: 150,000 (level 2)
					],
				},
				// Preparation: level 3 data (should use level 3 when requesting level 3)
				{
					snapshot: createBudgetSnapshot('snap-2', 'Preparation', 2),
					budgetItems: [
						createBudgetLineItem('item-3', '4', 40, 4000), // Architectural: 160,000 (level 3)
						createBudgetLineItem('item-4', '5', 60, 3500), // Structural: 210,000 (level 3)
						createBudgetLineItem('item-5', '6', 30, 2000), // HVAC: 60,000 (level 3)
						createBudgetLineItem('item-6', '7', 25, 1800), // Electrical: 45,000 (level 3)
					],
				},
				// Detailed budgets: level 4 data (should fall back to level 3 when requesting level 3)
				{
					snapshot: createBudgetSnapshot('snap-3', 'Concept Design', 3),
					budgetItems: [
						createBudgetLineItem('item-7', '8', 20, 2000), // Doors: 40,000 (level 4)
						createBudgetLineItem('item-8', '9', 25, 1800), // Finishes: 45,000 (level 4)
						createBudgetLineItem('item-9', '10', 35, 2200), // Concrete: 77,000 (level 4)
						createBudgetLineItem('item-10', '11', 15, 3000), // Steel: 45,000 (level 4)
					],
				},
				{
					snapshot: createBudgetSnapshot('snap-4', 'Spatial Coordination', 4),
					budgetItems: [
						createBudgetLineItem('item-11', '8', 22, 2100), // Doors: 46,200 (level 4)
						createBudgetLineItem('item-12', '9', 28, 1900), // Finishes: 53,200 (level 4)
						createBudgetLineItem('item-13', '10', 38, 2300), // Concrete: 87,400 (level 4)
						createBudgetLineItem('item-14', '11', 18, 3100), // Steel: 55,800 (level 4)
					],
				},
			];

			// Test level 3 visualization (the problematic case from the user's report)
			const result = createStackedBudgetData(realWorldWbsItems, realWorldSnapshots, 3);

			expect(result).toHaveLength(4);

			// Strategic snapshot should fall back to level 2 (nearest available)
			expect(result[0]).toEqual({
				snapshot: 'Strategic',
				'01.02': 500000, // Fallback from level 2
				'01.05': 150000, // Fallback from level 2
				'01.02.03': 0, // Not present in strategic snapshot
				'01.02.04': 0, // Not present in strategic snapshot
				'01.05.01': 0, // Not present in strategic snapshot
				'01.05.02': 0, // Not present in strategic snapshot
			});

			// Preparation snapshot should use level 3 data (exact match)
			expect(result[1]).toEqual({
				snapshot: 'Preparation',
				'01.02': 0, // Not used as fallback since level 3 data exists
				'01.05': 0, // Not used as fallback since level 3 data exists
				'01.02.03': 160000, // Level 3 data
				'01.02.04': 210000, // Level 3 data
				'01.05.01': 60000, // Level 3 data
				'01.05.02': 45000, // Level 3 data
			});

			// Detailed snapshots should aggregate level 4 data up to level 3
			// This tests the hierarchical rollup functionality
			expect(result[2]).toEqual({
				snapshot: 'Concept Design',
				'01.02': 0,
				'01.05': 0,
				'01.02.03': 85000, // 40,000 + 45,000 (doors + finishes)
				'01.02.04': 122000, // 77,000 + 45,000 (concrete + steel)
				'01.05.01': 0,
				'01.05.02': 0,
			});

			expect(result[3]).toEqual({
				snapshot: 'Spatial Coordination',
				'01.02': 0,
				'01.05': 0,
				'01.02.03': 99400, // 46,200 + 53,200 (doors + finishes)
				'01.02.04': 143200, // 87,400 + 55,800 (concrete + steel)
				'01.05.01': 0,
				'01.05.02': 0,
			});
		});
	});

	describe('createStackedBudgetChartConfig', () => {
		it('should create chart configuration for WBS codes', () => {
			const wbsCodes = ['01', '02'];
			const result = createStackedBudgetChartConfig(wbsCodes, wbsItems);

			// 	expect(result).toEqual({
			// 		'01': {
			// 			label: '01: Site Work',
			// 			color: '#440154', // interpolateViridis(0)
			// 		},
			// 		'02': {
			// 			label: '02: Structure',
			// 			color: '#fde725', // interpolateViridis(1)
			// 		},
			// 	});
		});

		it('should truncate long descriptions', () => {
			const longDescWbsItems = [
				createWbsItem('1', null, '01', 'Very Long Description That Should Be Truncated', 1),
			];
			const wbsCodes = ['01'];
			const result = createStackedBudgetChartConfig(wbsCodes, longDescWbsItems);

			expect(result['01'].label).toBe('01: Very Long Descr...');
		});
	});
});
